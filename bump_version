#!/usr/bin/env bash
#
# This script bump scrcpy version by editing all the necessary files.
#
# Usage:
#
#     ./bump_version 1.23.4
#
# Then check the diff manually to confirm that everything is ok.

set -e

if [[ $# != 1 ]]
then
    echo "Syntax: $0 <version>" >&2
    exit 1
fi

VERSION="$1"

a=( ${VERSION//./ } )
MAJOR="${a[0]:-0}"
MINOR="${a[1]:-0}"
PATCH="${a[2]:-0}"

# If VERSION is 1.23.4, then VERSION_CODE is 12304
VERSION_CODE="$(( $MAJOR * 10000 + $MINOR * 100 + "$PATCH" ))"

echo "$VERSION: major=$MAJOR minor=$MINOR patch=$PATCH [versionCode=$VERSION_CODE]"
sed -i "s/^\(\s*version: \)'[^']*'/\1'$VERSION'/" meson.build
sed -i "s/^\(\s*versionCode \).*/\1$VERSION_CODE/;s/^\(\s*versionName \).*/\1\"$VERSION\"/" server/build.gradle
sed -i "s/^\(SCRCPY_VERSION_NAME=\).*/\1$VERSION/" server/build_without_gradle.sh
sed -i "s/^\(\s*VALUE \"ProductVersion\", \)\"[^\"]*\"/\1\"$VERSION\"/" app/scrcpy-windows.rc
echo done
