# Video4Linux

On Linux, it is possible to send the video stream to a [v4l2] loopback device,
so that the Android device can be opened like a webcam by any v4l2-capable tool.

[v4l2]: https://en.wikipedia.org/wiki/Video4Linux

The module `v4l2loopback` must be installed:

```bash
sudo apt install v4l2loopback-dkms
```

To create a v4l2 device:

```bash
sudo modprobe v4l2loopback
```

This will create a new video device in `/dev/videoN`, where `N` is an integer
(more [options](https://github.com/umlaeute/v4l2loopback#options) are available
to create several devices or devices with specific IDs).

If you encounter problems detecting your device with Chrome/WebRTC, you can try
`exclusive_caps` mode:

```
sudo modprobe v4l2loopback exclusive_caps=1
```

To list the enabled devices:

```bash
# requires v4l-utils package
v4l2-ctl --list-devices

# simple but might be sufficient
ls /dev/video*
```

To start `scrcpy` using a v4l2 sink:

```bash
scrcpy --v4l2-sink=/dev/videoN
scrcpy --v4l2-sink=/dev/videoN --no-video-playback  # disable playback window
```

(replace `N` with the device ID, check with `ls /dev/video*`)

Once enabled, you can open your video stream with a v4l2-capable tool:

```bash
ffplay -i /dev/videoN
vlc v4l2:///dev/videoN   # VLC might add some buffering delay
```

For example, you could capture the video within [OBS] or within your video
conference tool.

[OBS]: https://obsproject.com/


## Buffering

By default, there is no video buffering, to get the lowest possible latency.

As for the [video display](video.md#buffering), it is possible to add
buffering to delay the v4l2 stream:

```bash
scrcpy --v4l2-buffer=300     # add 300ms buffering for v4l2 sink
```
