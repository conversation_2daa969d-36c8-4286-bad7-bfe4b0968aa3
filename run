#!/usr/bin/env bash
# Run scrcpy generated in the specified BUILDDIR.
#
# This provides the same feature as "ninja run", except that it is possible to
# pass arguments to scrcpy.
#
# Syntax: ./run BUILDDIR <scrcpy options ...>
if [[ $# = 0 ]]
then
    echo "Syntax: $0 BUILDDIR <scrcpy options>" >&2
    exit 1
fi

BUILDDIR="$1"
shift

if [[ ! -d "$BUILDDIR" ]]
then
    echo "The build dir \"$BUILDDIR\" does not exist." >&2
    exit 1
fi

SCRCPY_ICON_PATH="app/data/icon.png" \
SCRCPY_SERVER_PATH="$BUILDDIR/server/scrcpy-server" \
"$BUILDDIR/app/scrcpy" "$@"
